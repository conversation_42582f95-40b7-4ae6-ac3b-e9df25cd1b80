using System.Text.Json.Serialization;

namespace Winmug.Core.Models;

/// <summary>
/// Represents a SmugMug album with additional album-specific properties
/// </summary>
public class SmugMugAlbum
{
    [JsonPropertyName("AlbumKey")]
    public string AlbumKey { get; set; } = string.Empty;

    [JsonPropertyName("Name")]
    public string Name { get; set; } = string.Empty;

    [JsonPropertyName("Description")]
    public string? Description { get; set; }

    [JsonPropertyName("UrlName")]
    public string? UrlName { get; set; }

    [JsonPropertyName("UrlPath")]
    public string? UrlPath { get; set; }

    [JsonPropertyName("WebUri")]
    public string WebUri { get; set; } = string.Empty;

    [JsonPropertyName("Uri")]
    public string Uri { get; set; } = string.Empty;

    [JsonPropertyName("Date")]
    public DateTime? Date { get; set; }

    [JsonPropertyName("LastUpdated")]
    public DateTime? LastUpdated { get; set; }

    [JsonPropertyName("ImagesLastUpdated")]
    public DateTime? ImagesLastUpdated { get; set; }

    [JsonPropertyName("Privacy")]
    public string? Privacy { get; set; }

    [JsonPropertyName("SmugSearchable")]
    public object? SmugSearchable { get; set; }

    [JsonPropertyName("WorldSearchable")]
    public object? WorldSearchable { get; set; }

    [JsonPropertyName("PasswordHint")]
    public string? PasswordHint { get; set; }

    [JsonPropertyName("External")]
    public bool? External { get; set; }

    [JsonPropertyName("ImageCount")]
    public int? ImageCount { get; set; }

    [JsonPropertyName("TotalSizes")]
    public long? TotalSizes { get; set; }

    [JsonPropertyName("OriginalSizes")]
    public long? OriginalSizes { get; set; }

    [JsonPropertyName("AllowDownloads")]
    public bool? AllowDownloads { get; set; }

    [JsonPropertyName("Uris")]
    public SmugMugAlbumUris? Uris { get; set; }

    // Helper properties
    public string DisplayName => !string.IsNullOrEmpty(Name) ? Name : UrlName ?? AlbumKey;
}

/// <summary>
/// Contains URIs for SmugMug album-related resources
/// </summary>
public class SmugMugAlbumUris
{
    [JsonPropertyName("AlbumImages")]
    public SmugMugUriInfo? AlbumImages { get; set; }

    [JsonPropertyName("User")]
    public SmugMugUriInfo? User { get; set; }

    [JsonPropertyName("Node")]
    public SmugMugUriInfo? Node { get; set; }

    [JsonPropertyName("HighlightImage")]
    public SmugMugUriInfo? HighlightImage { get; set; }
}

/// <summary>
/// Response wrapper for SmugMug albums collection
/// </summary>
public class SmugMugAlbumsResponse
{
    [JsonPropertyName("Album")]
    public List<SmugMugAlbum> Album { get; set; } = new();
}

/// <summary>
/// Special API response for SmugMug user albums endpoint
/// This endpoint returns albums as a direct array in the Response field, with pagination at the root level
/// </summary>
public class SmugMugUserAlbumsApiResponse
{
    [JsonPropertyName("Response")]
    public List<SmugMugAlbum> Response { get; set; } = new();

    [JsonPropertyName("Code")]
    public int Code { get; set; }

    [JsonPropertyName("Message")]
    public string Message { get; set; } = string.Empty;

    [JsonPropertyName("Uri")]
    public string Uri { get; set; } = string.Empty;

    [JsonPropertyName("UriDescription")]
    public string? UriDescription { get; set; }

    [JsonPropertyName("EndpointType")]
    public string? EndpointType { get; set; }

    [JsonPropertyName("Locator")]
    public string? Locator { get; set; }

    [JsonPropertyName("LocatorType")]
    public string? LocatorType { get; set; }

    [JsonPropertyName("Pages")]
    public SmugMugPagingInfo? Pages { get; set; }

    [JsonPropertyName("Timing")]
    public object? Timing { get; set; }
}
