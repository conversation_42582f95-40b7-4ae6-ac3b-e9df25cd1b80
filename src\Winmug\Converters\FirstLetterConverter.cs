using System;
using System.Globalization;
using System.Windows.Data;

namespace Winmug.Converters
{
    /// <summary>
    /// Converts a string to its first letter in uppercase. Used for profile picture placeholders.
    /// </summary>
    public class FirstLetterConverter : IValueConverter
    {
        public static readonly FirstLetterConverter Instance = new();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            var text = value as string;
            if (string.IsNullOrEmpty(text))
                return "U"; // Default to "U" for User
                
            return text.Substring(0, 1).ToUpper();
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
