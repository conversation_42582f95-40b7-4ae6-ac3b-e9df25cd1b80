using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace Winmug.Core.Models;

/// <summary>
/// Represents a selectable album for download with selection state
/// </summary>
public class SelectableAlbum : INotifyPropertyChanged
{
    private bool _isSelected;
    private bool _isPasswordProtected;
    private bool _isPrivate;

    public string AlbumKey { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string UrlName { get; set; } = string.Empty;
    public int ImageCount { get; set; }
    public long EstimatedSizeBytes { get; set; }
    public DateTime DateCreated { get; set; }
    public DateTime DateModified { get; set; }
    public string Privacy { get; set; } = string.Empty;
    public string NodeId { get; set; } = string.Empty;
    public string ParentNodeId { get; set; } = string.Empty;
    public string FullPath { get; set; } = string.Empty;
    public bool AllowDownloads { get; set; } = true;
    public string WebUri { get; set; } = string.Empty;

    /// <summary>
    /// Whether this album is selected for download
    /// </summary>
    public bool IsSelected
    {
        get => _isSelected;
        set
        {
            if (_isSelected != value)
            {
                _isSelected = value;
                OnPropertyChanged();
            }
        }
    }

    /// <summary>
    /// Whether this album is password protected
    /// </summary>
    public bool IsPasswordProtected
    {
        get => _isPasswordProtected;
        set
        {
            if (_isPasswordProtected != value)
            {
                _isPasswordProtected = value;
                OnPropertyChanged();
            }
        }
    }

    /// <summary>
    /// Whether this album is private
    /// </summary>
    public bool IsPrivate
    {
        get => _isPrivate;
        set
        {
            if (_isPrivate != value)
            {
                _isPrivate = value;
                OnPropertyChanged();
            }
        }
    }

    /// <summary>
    /// Human-readable size estimate
    /// </summary>
    public string EstimatedSize => FormatBytes(EstimatedSizeBytes);

    /// <summary>
    /// Display name for the album
    /// </summary>
    public string DisplayName => !string.IsNullOrEmpty(Name) ? Name : UrlName ?? AlbumKey;

    /// <summary>
    /// Album type indicator (folder icon, password protected, etc.)
    /// </summary>
    public string AlbumTypeIcon
    {
        get
        {
            if (IsPasswordProtected) return "🔒";
            if (IsPrivate) return "🔐";
            return "📁";
        }
    }

    /// <summary>
    /// Privacy icon for the album
    /// </summary>
    public string PrivacyIcon
    {
        get
        {
            if (IsPasswordProtected) return "🔒";
            if (IsPrivate) return "🔐";
            return "";
        }
    }

    /// <summary>
    /// Privacy status description
    /// </summary>
    public string PrivacyStatus
    {
        get
        {
            if (IsPasswordProtected) return "Password protected";
            if (IsPrivate) return "Private";
            return "Public";
        }
    }

    /// <summary>
    /// Format bytes into human-readable string
    /// </summary>
    private static string FormatBytes(long bytes)
    {
        if (bytes == 0) return "0 B";
        
        string[] sizes = { "B", "KB", "MB", "GB", "TB" };
        int order = 0;
        double size = bytes;
        
        while (size >= 1024 && order < sizes.Length - 1)
        {
            order++;
            size /= 1024;
        }
        
        return $"{size:0.##} {sizes[order]}";
    }

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}
